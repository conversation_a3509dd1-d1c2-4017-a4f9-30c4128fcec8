import os
import json
import requests
import time
from dotenv import load_dotenv
from utils import append_json_list_to_csv


load_dotenv()


FRESHDESK_DOMAIN = os.getenv("FRESHDESK_DOMAIN")
FRESHDESK_API_KEY = os.getenv("FRESHDESK_API_KEY")
PASSWORD = "x"



def fetch_tickets(update_since):
    page = 1
    tickets = []
    while page < 301:
        url = f"https://{FRESHDESK_DOMAIN}/api/v2/tickets?include=stats,requester,description&order_by=updated_at&order_type=asc&per_page=100&page={page}&updated_since={update_since}"
        response = requests.get(url, auth=(FRESHDESK_API_KEY, PASSWORD))
        response_text = response.content.decode()

        if response.status_code == 200:
            response_json = json.loads(response_text)
            append_json_list_to_csv(response_json, "./raw_data/tickets.csv")
        else:
            print(f"Request failed with status code {response.status_code}")
            break
        time.sleep(3)
        print(str(page) + " Done")
        page += 1
    return tickets



def clean_tickets():
    # read the csv file and keep only the row where created_at is in the month of august 2025
