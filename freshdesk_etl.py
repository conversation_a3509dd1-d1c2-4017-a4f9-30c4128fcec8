import os
import json
import requests
import time
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv
from utils import append_json_list_to_csv


load_dotenv()


FRESHDESK_DOMAIN = os.getenv("FRESHDESK_DOMAIN")
FRESHDESK_API_KEY = os.getenv("FRESHDESK_API_KEY")
PASSWORD = "x"



def fetch_tickets(update_since):
    page = 1
    tickets = []
    while page < 301:
        url = f"https://{FRESHDESK_DOMAIN}/api/v2/tickets?include=stats,requester,description&order_by=updated_at&order_type=asc&per_page=100&page={page}&updated_since={update_since}"
        response = requests.get(url, auth=(FRESHDESK_API_KEY, PASSWORD))
        response_text = response.content.decode()

        if response.status_code == 200:
            response_json = json.loads(response_text)
            append_json_list_to_csv(response_json, "./raw_data/tickets.csv")
        else:
            print(f"Request failed with status code {response.status_code}")
            break
        time.sleep(3)
        print(str(page) + " Done")
        page += 1
    return tickets



def clean_tickets():
    """
    Read the CSV file and keep only the rows where created_at is in the month of August 2025.
    Save the filtered data to a new CSV file in the clean_data folder.
    """
    # Read the CSV file
    input_file = "./raw_data/tickets.csv"
    output_file = "./clean_data/tickets_august_2025.csv"

    try:
        # Read the CSV file
        df = pd.read_csv(input_file)

        # Convert created_at to datetime
        df['created_at'] = pd.to_datetime(df['created_at'])

        # Filter for August 2025
        august_2025_mask = (
            (df['created_at'].dt.year == 2025) &
            (df['created_at'].dt.month == 8)
        )

        # Filter the dataframe
        filtered_df = df[august_2025_mask]

        # Ensure the clean_data directory exists
        os.makedirs("./clean_data", exist_ok=True)

        # Save the filtered data to the clean_data folder
        filtered_df.to_csv(output_file, index=False)

        print(f"Successfully filtered tickets for August 2025.")
        print(f"Original dataset: {len(df)} tickets")
        print(f"Filtered dataset: {len(filtered_df)} tickets")
        print(f"Saved to: {output_file}")

        return filtered_df

    except FileNotFoundError:
        print(f"Error: Could not find the input file {input_file}")
        return None
    except Exception as e:
        print(f"Error processing tickets: {str(e)}")
        return None

