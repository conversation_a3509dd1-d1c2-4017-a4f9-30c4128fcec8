from datetime import datetime, timedelta
import os
import csv
from typing import List, Dict, Any

def iso_day_range(start_iso: str, end_iso: str):
    start = datetime.fromisoformat(start_iso.replace("Z", "+00:00"))
    end = datetime.fromisoformat(end_iso.replace("Z", "+00:00"))
    cur = start
    while cur < end:
        yield cur.isoformat().replace("+00:00", "Z")
        cur += timedelta(days=1)



def append_json_list_to_csv(rows: List[Dict[str, Any]], file_path: str) -> None:
    """
    Append a list of JSON-like dicts to a CSV file.
    - Creates the CSV with headers from the dict keys if it doesn't exist.
    - If it exists, appends rows; columns are aligned to the file's existing headers.
      Missing keys become empty cells; extra keys are ignored.

    Args:
        rows: List of dictionaries to persist.
        file_path: Path to the CSV file.
    """
    if not rows:
        return

    # Ensure directory exists
    os.makedirs(os.path.dirname(file_path) or ".", exist_ok=True)

    file_exists = os.path.isfile(file_path)

    if not file_exists:
        # Use union of keys across all rows to be robust
        header_keys = set()
        for r in rows:
            header_keys.update(r.keys())
        header = list(header_keys)
        with open(file_path, mode="w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=header, extrasaction="ignore")
            writer.writeheader()
            for r in rows:
                writer.writerow({k: r.get(k, "") for k in header})
        return

    # When file exists, read its header and append respecting existing columns
    with open(file_path, mode="r", newline="", encoding="utf-8") as f:
        reader = csv.reader(f)
        try:
            existing_header = next(reader)
        except StopIteration:
            existing_header = []

    # If file is empty (no header), initialize it now with union of keys
    if not existing_header:
        header_keys = set()
        for r in rows:
            header_keys.update(r.keys())
        existing_header = list(header_keys)
        with open(file_path, mode="w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=existing_header, extrasaction="ignore")
            writer.writeheader()
            for r in rows:
                writer.writerow({k: r.get(k, "") for k in existing_header})
        return

    # Append using existing header
    with open(file_path, mode="a", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=existing_header, extrasaction="ignore")
        for r in rows:
            writer.writerow({k: r.get(k, "") for k in existing_header})
