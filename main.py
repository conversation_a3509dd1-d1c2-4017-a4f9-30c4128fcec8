import freshdesk_etl
from utils import append_json_list_to_csv
from utils import iso_day_range

START_DATE = "2025-08-01T00:00:00.000Z"
END_DATE = "2025-08-03T00:00:00.000Z"



# for day_iso in iso_day_range(START_DATE, END_DATE):
#     tickets = freshdesk_etl.fetch_tickets(update_since=day_iso)
#     append_json_list_to_csv(tickets, "./raw_data/tickets.csv")
#     print(day_iso, len(tickets))


tickets = freshdesk_etl.fetch_tickets(START_DATE)
print(len(tickets))
